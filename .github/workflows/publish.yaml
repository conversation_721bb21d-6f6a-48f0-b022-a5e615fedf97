﻿name: GitHub release and Publish
on: [workflow_dispatch]
defaults:
  run:
    shell: pwsh
jobs:
  upload-pester-results:
    name: Run Pester and upload results
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Test with Pester
        shell: pwsh
        run: ./Test-Module.ps1
      - name: Upload test results
        uses: actions/upload-artifact@v3
        with:
          name: ubuntu-Unit-Tests
          path: Unit.Tests.xml
    if: ${{ always() }}
  publish-to-gallery:
    name: Publish to PowerShell Gallery
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Publish
        env:
          GitHubPAT: ${{ secrets.GitHubPAT }}
          NUGETAPIKEY: ${{ secrets.NUGETAPIKEY }}
        run: ./build.ps1 -Task Deploy