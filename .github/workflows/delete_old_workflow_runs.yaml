﻿name: Delete old workflow runs
on:
  workflow_dispatch:
    inputs:
      days:
        description: 'Days-worth of runs to keep for each workflow'
        required: true
        default: '0'
      minimum_runs:
        description: 'Minimum runs to keep for each workflow'
        required: true
        default: '1'
      delete_workflow_pattern:
        description: 'Name or filename of the workflow (if not set, all workflows are targeted)'
        required: false
      delete_workflow_by_state_pattern:
        description: 'Filter workflows by state: active, deleted, disabled_fork, disabled_inactivity, disabled_manually'
        required: true
        default: "ALL"
        type: choice
        options:
          - "ALL"
          - active
          - deleted
          - disabled_inactivity
          - disabled_manually
      delete_run_by_conclusion_pattern:
        description: 'Remove runs based on conclusion: action_required, cancelled, failure, skipped, success'
        required: true
        default: "ALL"
        type: choice
        options:
          - "ALL"
          - "Unsuccessful: action_required,cancelled,failure,skipped"
          - action_required
          - cancelled
          - failure
          - skipped
          - success
      dry_run:
        description: 'Logs simulated changes, no deletions are performed'
        required: false

jobs:
  del_runs:
    runs-on: ubuntu-latest
    permissions:
      actions: write
      contents: read
    steps:
      - name: Delete workflow runs
        uses: Mattraks/delete-workflow-runs@v2
        with:
          token: ${{ github.token }}
          repository: ${{ github.repository }}
          retain_days: ${{ github.event.inputs.days }}
          keep_minimum_runs: ${{ github.event.inputs.minimum_runs }}
          delete_workflow_pattern: ${{ github.event.inputs.delete_workflow_pattern }}
          delete_workflow_by_state_pattern: ${{ github.event.inputs.delete_workflow_by_state_pattern }}
          delete_run_by_conclusion_pattern: >-
            ${{
              startsWith(github.event.inputs.delete_run_by_conclusion_pattern, 'Unsuccessful:')
              && 'action_required,cancelled,failure,skipped'
              || github.event.inputs.delete_run_by_conclusion_pattern
            }}
          dry_run: ${{ github.event.inputs.dry_run }}